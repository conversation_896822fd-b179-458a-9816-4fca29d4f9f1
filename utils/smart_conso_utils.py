import os
from datetime import datetime, timedelta

import pytz
from dateutil.relativedelta import relativedelta

from utils.dict_utils import get, keydefaultdict

belgium_tz = pytz.timezone("Europe/Brussels")

CSV_METER_NAME: dict = {
    "FR": "Compteur communicant",
    "DE": "Kommunikationsfähiger Zähler",
}
CSV_HEADERS: dict = {
    "FR": ["Du (date)", "De (heure)", "Au (date)", "À (heure)", "Code EAN", "Compteur", "Type de compteur", "Registre", "Volume", "Unité", "Statut de validation"],
    "DE": ["Vom (Datum)", "Um (Uhrzeit)", "Bis zum (Datum)", "Um (Uhrzeit)", "EAN-Code", "Zähler", "Zählertyp", "Register", "Menge", "Einheit", "Validierungsstatus"],
}
CSV_REGISTRY_MAP: dict = {
    "FR": keydefaultdict(
        lambda x: x,  # return key as value if missing
        {
            "1.8.0": "Prélèvement Total",
            "1.8.1": "Prélèvement Jour",
            "1.8.2": "Prélèvement Nuit",
            "2.8.0": "Injection Total",
            "2.8.1": "Injection Jour",
            "2.8.2": "Injection Nuit",
        },
    ),
    "DE": keydefaultdict(
        lambda x: x,  # return key as value if missing
        {
            "1.8.0": "Entnahme gesamt",
            "1.8.1": "Entnahme Tag",
            "1.8.2": "Entnahme Nacht",
            "2.8.0": "Einspeisung gesamt",
            "2.8.1": "Einspeisung Tag",
            "2.8.2": "Einspeisung Nacht",
        },
    ),
}
CSV_STATUS_MAP: dict = {
    "FR": keydefaultdict(
        lambda x: x,  # return key as value if missing
        {
            "valide": "Lu",
            "estimated": "Estimé",
            "edited": "Corrigé",
            "": "Pas de consommation",
            None: "Pas de consommation",
        },
    ),
    "DE": keydefaultdict(
        lambda x: x,  # return key as value if missing
        {
            "valide": "Abgelesen",
            "estimated": "Geschätzt",
            "edited": "Berichtigt",
            "": "Kein Verbrauch",
            None: "Kein Verbrauch",
        },
    ),
}


def get_valid_date_ranges_from_params(energy: str, start_date: str, end_date: str, contract_date: list[tuple[str, str]]) -> list:
    start_date = datetime.fromisoformat(start_date)
    end_date = datetime.fromisoformat(end_date)

    # In case of gaz, set the data date to 6h
    if energy == "gaz":
        start_date = start_date.replace(hour=6)
        end_date = end_date.replace(hour=6)

    valid_date_ranges = []

    for _contract_start, _contract_end in contract_date:
        contract_start = datetime.strptime(_contract_start, "%Y%m%d")
        contract_end = datetime.strptime(_contract_end, "%Y%m%d")

        valid_start = max(start_date, contract_start)
        valid_end = min(end_date, contract_end)

        if valid_start <= valid_end:
            valid_date_ranges.append((belgium_tz.localize(valid_start), belgium_tz.localize(valid_end)))

    return valid_date_ranges if valid_date_ranges else []


def process_data_response(data):
    if not data:
        return []

    # Pre-calculate time slots
    time_slots = _get_time_slots()

    # Group data by reading type for O(1) lookup
    rows_by_type = {}
    for row in data:
        reading_type = row["readingtypeid"]
        if reading_type not in rows_by_type:
            rows_by_type[reading_type] = []
        rows_by_type[reading_type].append(row)

    # Sort each group by measurement time
    for reading_type in rows_by_type:
        rows_by_type[reading_type].sort(key=lambda x: x["measuredatetime"])

    result = []
    previous_row = {}

    # for each reading type, we loop through all time slots
    for reading_type_id in rows_by_type:
        type_rows = rows_by_type[reading_type_id]
        row_index = 0
        time_slots_index = 0
        # we then iterate through all data rows and all time slots at the same time
        # this is more humanly complex, but avoid having to do a search loop after and having an O(n²) complexity
        while time_slots_index < len(time_slots):
            slot_start, slot_end = time_slots[time_slots_index]

            # create empty record for the missing slot in case there are no more data rows left
            if row_index >= len(type_rows):
                result.append(_create_empty_record(type_rows[0], reading_type_id, slot_start, slot_end))
                time_slots_index += 1
                continue

            row = type_rows[row_index]

            # calculate consumption and measuredatetime_from if consumption is already present, it means the row stayed the same and is already processed
            if "consumption" not in row:
                localized_time = belgium_tz.localize(row["measuredatetime"]) if row["measuredatetime"].tzinfo is None else row["measuredatetime"].astimezone(belgium_tz)
                row["measuredatetime"] = localized_time.isoformat()

                group_key = (row["ean"], row["meterid"], row["readingfrequency"], row["readingtypeid"])
                if group_key in previous_row:
                    row["consumption"] = round(row["measurevalue"] - previous_row[group_key]["measurevalue"], 3)
                    row["measuredatetime_from"] = previous_row[group_key]["measuredatetime"]
                else:
                    row["consumption"] = None
                    row["measuredatetime_from"] = None
                previous_row[group_key] = row

            # drop first empty consumption of each reading type
            # or drop rows that are before the current slot (this happens when we switch between summer and winter time, what a mess)
            if row["consumption"] is None or slot_start > row["measuredatetime_from"]:
                row_index += 1
                continue

            # keep the data row that falls within the current slot and move to the next one
            if slot_start <= row["measuredatetime_from"] < slot_end:
                result.append(row)
                row_index += 1
            # else, create an empty record for the missing slot and keep looking at the same data row
            else:
                result.append(_create_empty_record(type_rows[0], reading_type_id, slot_start, slot_end))

            time_slots_index += 1

    return result


def _get_time_slots(energy: str, start_date: str, end_date: str, slot_type: str) -> list[tuple[str, str]]:
    """Generate all time slots between start and end dates based on record type."""
    start_date = datetime.fromisoformat(start_date)
    end_date = datetime.fromisoformat(end_date) + relativedelta(days=1)

    if slot_type == "HOURLY":
        delta = timedelta(minutes=15) if energy == "elec" else timedelta(hours=1)
    elif slot_type == "DAILY":
        delta = timedelta(days=1)
    else:  # MONTHLY
        delta = relativedelta(months=1)

    slots = []
    current = start_date
    while current < end_date:
        next_slot = min((current + delta).replace(day=1), end_date) if slot_type == "MONTHLY" else min(current + delta, end_date)
        slots.append((belgium_tz.localize(current).isoformat(), belgium_tz.localize(next_slot).isoformat()))
        current = next_slot

    return slots


def _create_empty_record(template: dict, reading_type_id: str, start_time: str, end_time: str) -> dict:
    """Create an empty consumption record based on template."""
    return {
        "meterid": template["meterid"],
        "measuredatetime": end_time,
        "measuredatetime_from": start_time,
        "measurevalue": None,
        "readingtypeid": reading_type_id,
        "measurestate": None,
        "ean": template["ean"],
        "readingfrequency": template["readingfrequency"],
        "standardizationtimestamp": None,
        "measureunit": template["measureunit"],
        "consumption": None,
    }


def data_to_csv(processed_data: list[dict]) -> str:
    """
    Convert processed data into a CSV-formatted string.

    Returns
    -------
    str
        A CSV-formatted string containing the processed data with semicolon (`;`) delimiter.
        Each row in the string corresponds to a data entry with headers included as the
        first row.

    """
    csv_rows = [CSV_HEADERS[os.environ["LANG"]]]
    for row in processed_data:
        from_date_str = from_time_str = to_date_str = to_time_str = ""
        if "measuredatetime_from" in row:
            from_date = datetime.fromisoformat(row["measuredatetime_from"])
            from_date_str = from_date.strftime("%d-%m-%y")
            from_time_str = from_date.strftime("%H:%M:%S")
        if "measuredatetime" in row:
            to_date = datetime.fromisoformat(row["measuredatetime"])
            to_date_str = to_date.strftime("%d-%m-%y")
            to_time_str = to_date.strftime("%H:%M:%S")
        csv_rows.append(
            [
                from_date_str,
                from_time_str,
                to_date_str,
                to_time_str,
                get(row, "ean", ""),
                get(row, "meterid", ""),
                CSV_METER_NAME[os.environ["LANG"]],
                CSV_REGISTRY_MAP[os.environ["LANG"]][row.get("readingtypeid", "")],
                get(row, "consumption", "0.0"),
                get(row, "measureunit", ""),
                CSV_STATUS_MAP[os.environ["LANG"]][row.get("measurestate", "")],
            ],
        )
    return "\n".join([";".join(map(str, row)) for row in csv_rows])

#!/usr/bin/env python3
"""
Test script to verify that tempfile.mkstemp is working correctly
in the updated export functionality.
"""

import os
import tempfile
import zipfile
from unittest.mock import MagicMock, patch

def test_tempfile_mkstemp_usage():
    """Test that tempfile.mkstemp creates and cleans up files properly."""
    
    # Test basic mkstemp functionality
    fd, temp_path = tempfile.mkstemp(suffix='.test', prefix='export_test_')
    
    try:
        # Verify file was created
        assert os.path.exists(temp_path), "Temporary file should exist"
        
        # Write some test data
        with os.fdopen(fd, 'w') as f:
            f.write("Test data")
        
        # Read back the data
        with open(temp_path, 'r') as f:
            content = f.read()
            assert content == "Test data", "Content should match"
        
        print("✓ Basic tempfile.mkstemp test passed")
        
    finally:
        # Clean up
        try:
            os.unlink(temp_path)
        except OSError:
            pass

def test_zip_creation_with_tempfile():
    """Test ZIP creation using tempfile.mkstemp similar to export_ean_data.py"""
    
    # Create test data
    test_files = {
        'file1.csv': 'header1,header2\nvalue1,value2\n',
        'file2.csv': 'header3,header4\nvalue3,value4\n'
    }
    
    # Create temporary ZIP file
    temp_fd, temp_zip_path = tempfile.mkstemp(suffix='.zip', prefix='export_test_')
    
    try:
        # Create ZIP file
        with os.fdopen(temp_fd, 'wb') as temp_zip_file:
            with zipfile.ZipFile(temp_zip_file, "w", zipfile.ZIP_DEFLATED) as zip_file:
                for filename, content in test_files.items():
                    zip_file.writestr(filename, content)
        
        # Verify ZIP file was created and contains expected files
        assert os.path.exists(temp_zip_path), "ZIP file should exist"
        
        with zipfile.ZipFile(temp_zip_path, 'r') as zip_file:
            zip_contents = zip_file.namelist()
            assert 'file1.csv' in zip_contents, "file1.csv should be in ZIP"
            assert 'file2.csv' in zip_contents, "file2.csv should be in ZIP"
            
            # Verify content
            content1 = zip_file.read('file1.csv').decode('utf-8')
            assert content1 == test_files['file1.csv'], "Content should match"
        
        print("✓ ZIP creation with tempfile.mkstemp test passed")
        
    finally:
        # Clean up
        try:
            os.unlink(temp_zip_path)
        except OSError:
            pass

def test_export_ean_data_class():
    """Test the ExportEanData class temporary file handling."""
    
    # Mock the necessary imports and dependencies
    with patch('sqlDriven.controllers.ExportEanData.ws205'), \
         patch('sqlDriven.controllers.ExportEanData.upload_s3_file'):
        
        from sqlDriven.controllers.ExportEanData import ExportEanData
        
        # Create test event
        test_event = {
            'export_id': 'test_export_123',
            'eans': [
                {'ean': '123456789012345678', 'meter': 'meter1', 'contract_date': []}
            ]
        }
        
        # Create instance
        export_instance = ExportEanData(test_event, {})
        
        # Verify temp_files is initialized
        assert hasattr(export_instance, 'temp_files'), "Should have temp_files attribute"
        assert export_instance.temp_files == {}, "temp_files should be empty initially"
        
        # Test cleanup method exists
        assert hasattr(export_instance, 'cleanup_temp_files'), "Should have cleanup_temp_files method"
        
        print("✓ ExportEanData class initialization test passed")

if __name__ == "__main__":
    print("Testing tempfile.mkstemp usage...")
    
    test_tempfile_mkstemp_usage()
    test_zip_creation_with_tempfile()
    test_export_ean_data_class()
    
    print("\n✅ All tests passed! tempfile.mkstemp is working correctly.")
